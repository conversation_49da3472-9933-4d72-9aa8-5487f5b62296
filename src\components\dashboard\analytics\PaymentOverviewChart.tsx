"use client";
import React, { useState, useRef } from "react";
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  ChartOptions,
} from "chart.js";
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { Doughnut } from "react-chartjs-2";
import { PaymentOverviewData } from "./types";
import DateRangeFilter from "@/components/common/DateRangeFilter";
import CancellationChart from "../CancellationChart";
import Image from "next/image";

ChartJS.register(ArcElement, Tooltip, Legend, ChartDataLabels);

interface CancellationData {
  cancelled_sessions: Array<{
    name: string;
    session: number;
  }>;
  total_cancelled_sessions: number;
  collected_fees: number;
  pending_fees: number;
  loss_due_to_cancellation: number;
  total_session_fees: number;
}

interface PaymentOverviewChartProps {
  data: PaymentOverviewData;
  cancellationData?: CancellationData;
  onPaymentDateChange?: (startDate: Date, endDate: Date) => void;
  onCancellationDateChange?: (startDate: Date, endDate: Date) => void;
}

const PaymentOverviewChart: React.FC<PaymentOverviewChartProps> = ({
  data,
  cancellationData,
  onPaymentDateChange,
  onCancellationDateChange,
}) => {
  const [selectedView, setSelectedView] = useState<'payment' | 'cancellation'>('payment');
  const [startX, setStartX] = useState<number | null>(null);
  const chartContainerRef = useRef<HTMLDivElement>(null);

  // Handle touch/mouse events for swipe
  const handleTouchStart = (e: React.TouchEvent | React.MouseEvent) => {
    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
    setStartX(clientX);
  };

  const handleTouchEnd = (e: React.TouchEvent | React.MouseEvent) => {
    if (startX === null) return;

    const clientX = 'changedTouches' in e ? e.changedTouches[0].clientX : e.clientX;
    const diffX = startX - clientX;
    const threshold = 50; // Minimum swipe distance

    if (Math.abs(diffX) > threshold) {
      if (diffX > 0) {
        // Swipe left - go to cancellation
        setSelectedView('cancellation');
      } else {
        // Swipe right - go to payment
        setSelectedView('payment');
      }
    }
    setStartX(null);
  };

  // Check if chart has data
  const hasData = data.totalPayment > 0 && (
    data.receivedAfterSessions.amount > 0 ||
    data.cancellationFees.amount > 0 ||
    data.advancePayment.amount > 0
  );

  const chartData = {
    labels: ["Received After Sessions", "Cancellation Fees", "Advance Payment"],
    datasets: [
      {
        data: [
          data.receivedAfterSessions.amount,
          data.cancellationFees.amount,
          data.advancePayment.amount,
        ],
        backgroundColor: ["#FBE9D0", "#FFC7CE", "#D8E5FF"],
        borderWidth: 0,
        cutout: "70%",
      },
    ],
  };

  // Calculate percentages consistently for both chart and legends
  const calculatePercentage = (value: number, total: number): string => {
    if (total === 0 || !value || isNaN(Number(value))) {
      return '0.00';
    }
    return ((Number(value) / total) * 100).toFixed(2);
  };

  const totalAmount = data.receivedAfterSessions.amount + data.cancellationFees.amount + data.advancePayment.amount;

  const receivedPercentage = calculatePercentage(data.receivedAfterSessions.amount, totalAmount);
  const cancellationPercentage = calculatePercentage(data.cancellationFees.amount, totalAmount);
  const advancePercentage = calculatePercentage(data.advancePayment.amount, totalAmount);

  const options: ChartOptions<"doughnut"> = {
    responsive: true,
    maintainAspectRatio: true,
    plugins: {
      legend: {
        display: false,
      },
      datalabels: {
        display: true,
        color: (context) => {
          // Return different colors based on the segment
          switch(context.dataIndex) {
            case 0: return '#D8A155'; // Red for first segment
            case 1: return '#DD6877'; // Black for second segment
            case 2: return '#5B84D5'; // Green for third segment
            default: return 'black'; // Default to black
          }
        },
        font: {
          weight: 'bold',
          size: 14,
        },
        formatter: (value, context) => {
          const total = context.dataset.data.reduce((a: number, b: unknown) => a + (Number(b) || 0), 0);
          if (total === 0 || !value || isNaN(Number(value))) {
            return '';
          }
          const percentage = ((Number(value) / total) * 100).toFixed(2);
          return `${percentage}%`;
        },
      },
      tooltip: {
        backgroundColor: "#2D3134",
        titleColor: "#fff",
        bodyColor: "#fff",
        borderColor: "#E6E6E6",
        borderWidth: 1,
        cornerRadius: 8,
        callbacks: {
          label: function (context) {
            const label = context.label || "";
            const value = context.parsed;
            const total = context.dataset.data.reduce((a: number, b: unknown) => a + (Number(b) || 0), 0);
            const percent = ((Number(value) / total) * 100).toFixed(2);
            return `${label}: ₹${value} (${percent}%)`;
          },
        },
      },
    },
  };

  return (
    <>
      <div className="bg-white p-3 sm:p-5 rounded-lg shadow-sm h-[564px] w-full md:w-[358px] 2xl:w-[358px] flex flex-col" style={{ fontFamily: 'Poppins, sans-serif' }}>
        {/* Header with Sliding Text */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 sm:mb-6">
          <div className="flex items-center gap-3 mb-2 sm:mb-0">
            {/* Mobile: Full width text without animation */}
            <div className="block sm:hidden w-full">
              <h3 className="text-base font-semibold text-primary">
                {selectedView === 'payment' ? 'Health of Your Practice, in Numbers 😍' : 'Cancellation Overview'}
              </h3>
            </div>
            {/* Desktop: Sliding Text Container */}
            <div className="hidden sm:block w-[154px] overflow-hidden">
              <div className="animate-slide-left whitespace-nowrap">
                <h3 className="text-lg font-semibold text-primary inline-block">
                  {selectedView === 'payment' ? 'Health of Your Practice, in Numbers 😍' : 'Cancellation Overview'}
                </h3>
              </div>
            </div>
          </div>
          <DateRangeFilter
            allowFutureDates={true}
            onApply={(startDate, endDate) => {
              // Update both payment and cancellation data when filter changes
              if (onPaymentDateChange) {
                onPaymentDateChange(startDate, endDate);
              }
              if (onCancellationDateChange) {
                onCancellationDateChange(startDate, endDate);
              }
            }}
          />
        </div>

        {/* Swipeable Content Container */}
        <div
          ref={chartContainerRef}
          className="flex-1 flex flex-col"
          onTouchStart={handleTouchStart}
          onTouchEnd={handleTouchEnd}
          onMouseDown={handleTouchStart}
          onMouseUp={handleTouchEnd}
          style={{ cursor: 'grab' }}
        >
          {selectedView === 'payment' ? (
            <>
              {/* Top Stats */}
              <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 sm:mb-6 gap-3 sm:gap-0">
                <div className="flex items-center gap-2">
                  <div className="text-lg sm:text-[20px] font-semibold">₹{data.avgSessionFee}</div>
                  <div className="text-xs sm:text-[14px] font-medium text-[#2D3134]">Avg/Session Fee</div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="text-lg sm:text-[20px] font-semibold">{data.noOfSessions}</div>
                  <div className="text-xs sm:text-[14px] font-medium text-[#2D3134]">No.Of Session</div>
                </div>
              </div>

              {/* Chart Container */}
              <div className="flex flex-col items-center flex-1">
                {/* Chart */}
                <div className="h-[200px] w-[200px] sm:h-[250px] sm:w-[250px] relative mb-4">
                  {hasData ? (
                    <>
                      <Doughnut data={chartData} options={options} />
                      {/* Center Text */}
                      <div className="absolute inset-0 flex flex-col items-center justify-center">
                        <div className="text-sm sm:text-base font-bold text-primary">₹{data.totalPayment}</div>
                        <div className="text-xs text-gray-500">Total Payment</div>
                      </div>
                    </>
                  ) : (
                    /* Empty State */
                    <div className="flex items-center justify-center h-full w-full">
                      <div className="text-center px-4">
                        <p className="text-sm text-gray-500 leading-relaxed">
                          Once you start recording payments, this space will give you a pulse on your practice&apos;s financial health.
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Payment Legend */}
                {hasData && (
                  <div className="space-y-4 w-full max-w-xs pt-4">
                    {/* First Legend Item */}
                    <div className="flex items-center justify-between group relative">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-[#FBE9D0]"></div>
                        <span className="text-sm text-gray-700">Received After Sessions</span>
                      </div>
                      <span className="text-sm font-medium">
                        {receivedPercentage}%
                      </span>
                      {/* Tooltip */}
                      <div className="absolute left-0 bottom-full mb-2 hidden group-hover:block bg-gray-800 text-white text-xs rounded-lg p-3 w-64 z-50">
                        Signals how much of your work is being financially compensated - an anchor for sustainable practice.
                        <div className="absolute top-full left-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                      </div>
                    </div>
                    <div className="border-t border-dashed border-gray-100"></div>

                    {/* Second Legend Item */}
                    <div className="flex items-center justify-between group relative">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-[#FFCACC]"></div>
                        <span className="text-sm text-gray-700">Cancellation Dues Collected</span>
                      </div>
                      <span className="text-sm font-medium">
                        {cancellationPercentage}%
                      </span>
                      {/* Tooltip */}
                      <div className="absolute left-0 bottom-full mb-2 hidden group-hover:block bg-gray-800 text-white text-xs rounded-lg p-3 w-64 z-50">
                        Shows how well your therapy frame is holding. Protected time = protected income.
                        <div className="absolute top-full left-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                      </div>
                    </div>
                    <div className="border-t border-dashed border-gray-100"></div>

                    {/* Third Legend Item */}
                    <div className="flex items-center justify-between group relative">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-[#A9C6FF]"></div>
                        <span className="text-sm text-gray-700">Advance Payment</span>
                      </div>
                      <span className="text-sm font-medium">
                        {advancePercentage}%
                      </span>
                      {/* Tooltip */}
                      <div className="absolute left-0 bottom-full mb-2 hidden group-hover:block bg-gray-800 text-white text-xs rounded-lg p-3 w-64 z-50">
                        Signals steady cashflow and client commitment. A sign your practice is building security.
                        <div className="absolute top-full left-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </>
          ) : (
            <>
              {/* Cancellation Overview Content */}
              {cancellationData && cancellationData.cancelled_sessions && cancellationData.cancelled_sessions.length > 0 ? (
                <>
                  {/* Top Stats for Cancellation */}
                  {/* <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 sm:mb-6 gap-3 sm:gap-0">
                    <div className="flex items-center gap-2">
                      <div className="text-lg sm:text-[20px] font-semibold">₹{cancellationData.collected_fees}</div>
                      <div className="text-xs sm:text-[14px] font-medium text-[#2D3134]">Collected Fees</div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="text-lg sm:text-[20px] font-semibold">₹{cancellationData.pending_fees}</div>
                      <div className="text-xs sm:text-[14px] font-medium text-[#2D3134]">Pending Fees</div>
                    </div>
                  </div> */}

                  {/* Cancellation Chart */}
                  <div className="flex flex-col items-center flex-1 pt-8">
                    <div className="h-[200px] w-[200px] sm:h-[250px] sm:w-[250px] relative mb-4">
                      <CancellationChart cancellationData={cancellationData} />
                    </div>

                    {/* Cancellation Legend */}
                    <div className="space-y-6 w-full max-w-xs pt-8">
                      {/* Collected Fees */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-gray-700">Collected Fees</span>
                        </div>
                        <span className="text-sm font-medium">₹{cancellationData.collected_fees}</span>
                      </div>
                      <div className="border-t border-dashed border-gray-100"></div>

                      {/* Pending Fees */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-gray-700">Pending Fees</span>
                        </div>
                        <span className="text-sm font-medium">₹{cancellationData.pending_fees}</span>
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <div className="flex flex-col items-center justify-center flex-1">
                  <Image
                    width={1000}
                    height={1000}
                    src="/assets/images/dashboard/not-found-client--list.webp"
                    alt="No Cancellation Records"
                    className="w-[221px] h-auto"
                  />
                  <p className="text-lg sm:text-xl font-semibold pt-3.5 text-primary">
                    No Cancellation Records Yet
                  </p>
                </div>
              )}
            </>
          )}
        </div>

        {/* Carousel Dots */}
        <div className="flex justify-center gap-2 mt-4">
          <button
            onClick={() => setSelectedView('payment')}
            className={`w-2 h-2 rounded-full transition-colors duration-200 ${
              selectedView === 'payment' ? 'bg-primary' : 'bg-gray-300'
            }`}
          />
          <button
            onClick={() => setSelectedView('cancellation')}
            className={`w-2 h-2 rounded-full transition-colors duration-200 ${
              selectedView === 'cancellation' ? 'bg-primary' : 'bg-gray-300'
            }`}
          />
        </div>
      </div>
    </>
  );
};

export default PaymentOverviewChart;
